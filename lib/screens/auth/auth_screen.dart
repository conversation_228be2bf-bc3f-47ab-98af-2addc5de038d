import 'package:fasttime/screens/auth/widgets/build_input_label.dart';
import 'package:fasttime/screens/auth/widgets/build_text_field.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fasttime/blocs/auth/auth_bloc.dart';
import 'package:fasttime/blocs/auth/auth_event.dart';
import 'package:fasttime/blocs/auth/auth_state.dart';
import 'package:fasttime/painters/gradient_wave_painter.dart';

import '../../utils/validator_utils.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  late bool isLoading = false;

  late AnimationController _backgroundAnimationController;
  late AnimationController _formAnimationController;
  late Animation<double> _formAnimation;

  bool _isSignIn = true; // Local state for form mode

  @override
  void initState() {
    super.initState();

    _backgroundAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat(reverse: true);

    _formAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _formAnimation = CurvedAnimation(
      parent: _formAnimationController,
      curve: Curves.easeOutQuart,
    );

    _formAnimationController.forward();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _backgroundAnimationController.dispose();
    _formAnimationController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final name = _nameController.text.trim();

    final bloc = context.read<AuthBloc>();
    if (_isSignIn) {
      bloc.add(
        SignInWithEmailPasswordRequested(email: email, password: password),
      );
    } else {
      bloc.add(
        SignUpWithEmailPasswordRequested(
          fullName: name,
          email: email,
          password: password,
        ),
      );
    }
  }

  void _resetForm() {
    _emailController.clear();
    _passwordController.clear();
    _nameController.clear();
    _formKey.currentState?.reset();
    _formAnimationController.reset();
    _formAnimationController.forward();
  }

  void _toggleAuthMode() {
    setState(() {
      _isSignIn = !_isSignIn;
    });
    _resetForm();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is Authenticated) {
          // Use go instead of pushReplacementNamed to avoid navigation conflicts
          GoRouter.of(context).go('/');
        } else if (state is AuthFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message), backgroundColor: Colors.red),
          );
        }
        isLoading = state is AuthLoading;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF121212),
        body: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF780206), Color(0xFF061161)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Stack(
            children: [
              Positioned.fill(
                child: AnimatedBuilder(
                  animation: _backgroundAnimationController,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: GradientWavePainter(
                        animation: _backgroundAnimationController,
                        colors: const [Color(0xFF6441A5), Color(0xFF2A0845)],
                      ),
                    );
                  },
                ),
              ),
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 40),
                      _buildHeader(_isSignIn),
                      const SizedBox(height: 40),
                      FadeTransition(
                        opacity: _formAnimation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 0.1),
                            end: Offset.zero,
                          ).animate(_formAnimation),
                          child: _buildAuthForm(
                            isLoading: isLoading,
                            isSignIn: _isSignIn,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (isLoading)
                Container(
                  color: Colors.black54,
                  child: const Center(
                    child: CircularProgressIndicator(color: Color(0xFF6B4EFF)),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isSignIn) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'FasTime',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 32,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          isSignIn ? 'Welcome back' : 'Create your account',
          style: GoogleFonts.dmSans(
            color: Colors.white,
            fontSize: 28,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          isSignIn
              ? 'Sign in to continue your fasting journey'
              : 'Start your fasting journey today',
          style: GoogleFonts.dmSans(color: Colors.white70, fontSize: 16),
        ),
      ],
    );
  }

  Widget _buildAuthForm({required bool isLoading, required bool isSignIn}) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isSignIn) ...[
            buildInputLabel('Full Name'),
            const SizedBox(height: 8),
            buildTextField(
              _nameController,
              'Enter your name',
              Icons.person_outline,
              enabled: !isLoading,
              validator: validateName,
            ),
            const SizedBox(height: 24),
          ],
          buildInputLabel('Email'),
          const SizedBox(height: 8),
          buildTextField(
            _emailController,
            'Enter your email',
            Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            enabled: !isLoading,
            validator: validateEmail,
          ),
          const SizedBox(height: 24),
          buildInputLabel('Password'),
          const SizedBox(height: 8),
          buildTextField(
            _passwordController,
            'Enter your password',
            Icons.lock_outline,
            obscureText: true,
            onSubmitted: (_) => _submitForm(),
            enabled: !isLoading,
            validator: validatePassword,
          ),
          const SizedBox(height: 16),
          if (isSignIn)
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: isLoading
                    ? null
                    : () {
                        // TODO: Implement forgot password functionality
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Forgot password feature coming soon!',
                              style: GoogleFonts.dmSans(color: Colors.white),
                            ),
                            backgroundColor: Colors.blue,
                          ),
                        );
                      },
                child: Text(
                  'Forgot Password?',
                  style: GoogleFonts.dmSans(color: const Color(0xFF6B4EFF)),
                ),
              ),
            ),
          const SizedBox(height: 32),
          _buildSubmitButton(isLoading: isLoading, isSignIn: isSignIn),
          const SizedBox(height: 24),
          _buildToggleButton(isSignIn, isLoading),
          const SizedBox(height: 40),
          _buildSocialButtons(isLoading),
        ],
      ),
    );
  }

  Widget _buildSubmitButton({required bool isLoading, required bool isSignIn}) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6B4EFF),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          disabledBackgroundColor: const Color(0xFF6B4EFF).withOpacity(0.5),
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                isSignIn ? 'Sign In' : 'Create Account',
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildToggleButton(bool isSignIn, bool isLoading) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          isSignIn ? "Don't have an account?" : 'Already have an account?',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        TextButton(
          onPressed: isLoading ? null : _toggleAuthMode,
          child: Text(
            isSignIn ? 'Sign Up' : 'Sign In',
            style: GoogleFonts.dmSans(
              color: const Color(0xFF6B4EFF),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialButtons(bool isLoading) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: Divider(color: Colors.white.withOpacity(0.2))),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Or continue with',
                style: GoogleFonts.dmSans(color: Colors.white54),
              ),
            ),
            Expanded(child: Divider(color: Colors.white.withOpacity(0.2))),
          ],
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialIcon(
              'assets/icons/google.png',
              () => context.read<AuthBloc>().add(SignInWithGoogleRequested()),
              isLoading,
            ),
            const SizedBox(width: 24),
            // _buildSocialIcon(
            //   'assets/icons/apple.png',
            //       () => context.read<AuthBloc>().add(S()),
            //   isLoading,
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialIcon(String icon, VoidCallback onTap, bool isLoading) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Opacity(
        opacity: isLoading ? 0.5 : 1.0,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.08),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withOpacity(0.1)),
          ),
          child: Center(child: Image.asset(icon, width: 24, height: 24)),
        ),
      ),
    );
  }
}
